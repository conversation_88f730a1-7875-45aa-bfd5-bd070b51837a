"""
Test script for GraphViz configuration improvements.

This script tests the new GraphViz configuration to ensure all layout types
work correctly and produce the expected attributes.
"""

from graphviz_config import (
    get_layout_config, 
    get_node_config, 
    get_edge_config, 
    list_available_layouts,
    DEFAULT_LAYOUT
)

def test_layout_configs():
    """Test all available layout configurations."""
    print("Testing GraphViz Layout Configurations")
    print("=" * 50)
    
    # Test default layout
    print(f"Default layout: {DEFAULT_LAYOUT}")
    default_config = get_layout_config()
    print(f"Default config: {default_config}")
    print()
    
    # Test all available layouts
    layouts = list_available_layouts()
    print("Available layouts:")
    for layout_name, description in layouts.items():
        print(f"  {layout_name}: {description}")
        config = get_layout_config(layout_name)
        print(f"    Layout engine: {config.get('layout', 'not specified')}")
        print(f"    Splines: {config.get('splines', 'not specified')}")
        print()

def test_node_config():
    """Test node configuration."""
    print("Testing Node Configuration")
    print("=" * 30)
    
    node_config = get_node_config()
    print("Node configuration:")
    for key, value in node_config.items():
        print(f"  {key}: {value}")
    print()

def test_edge_config():
    """Test edge configuration."""
    print("Testing Edge Configuration")
    print("=" * 30)
    
    edge_config = get_edge_config()
    print("Edge configuration:")
    for key, value in edge_config.items():
        print(f"  {key}: {value}")
    print()

def test_invalid_layout():
    """Test handling of invalid layout type."""
    print("Testing Invalid Layout Handling")
    print("=" * 35)
    
    invalid_config = get_layout_config('nonexistent_layout')
    print(f"Invalid layout falls back to: {invalid_config.get('layout', 'unknown')}")
    print()

def main():
    """Run all tests."""
    print("GraphViz Configuration Test Suite")
    print("=" * 60)
    print()
    
    test_layout_configs()
    test_node_config()
    test_edge_config()
    test_invalid_layout()
    
    print("All tests completed successfully!")
    print()
    print("Key Improvements Summary:")
    print("- Orthogonal edge routing for cleaner lines")
    print("- Better node spacing to reduce overlaps")
    print("- Edge concentration to merge multiple connections")
    print("- Consistent node sizing for better alignment")
    print("- Enhanced edge styling for better visibility")

if __name__ == "__main__":
    main()
