"""
Example usage of the improved GraphViz dependency diagram generator.

This example shows how to use different layout types and configurations
to generate cleaner, more organized dependency diagrams.
"""

# Example of how to use the improved GraphViz generator

def generate_dependency_diagram_examples():
    """
    Example function showing how to generate dependency diagrams
    with different layout configurations.
    """
    
    # Example 1: Default hierarchical layout (recommended for most cases)
    print("Example 1: Hierarchical Layout (Default)")
    print("-" * 40)
    
    # This will use the default 'hierarchical' layout with orthogonal edges
    controller = GraphVizGeneratorController('EXAMPLE_COMPONENT')
    graph_file = controller.generate_graph()
    print(f"Generated graph: {graph_file}")
    print("Features: Top-down layout, orthogonal edges, minimal crossings")
    print()
    
    # Example 2: Circular layout for relationship visualization
    print("Example 2: Circular Layout")
    print("-" * 25)
    
    # Use circular layout for showing relationships in complex networks
    graph_file = controller.generate_graph(layout_type='circular')
    print(f"Generated graph: {graph_file}")
    print("Features: Circular arrangement, curved edges, good for relationships")
    print()
    
    # Example 3: Force-directed layout for complex networks
    print("Example 3: Force-Directed Layout")
    print("-" * 30)
    
    # Use force-directed layout for natural clustering
    graph_file = controller.generate_graph(layout_type='force_directed')
    print(f"Generated graph: {graph_file}")
    print("Features: Physics-based positioning, natural clustering")
    print()
    
    # Example 4: Compact layout for dense graphs
    print("Example 4: Compact Layout")
    print("-" * 22)
    
    # Use compact layout for space-efficient visualization
    graph_file = controller.generate_graph(layout_type='compact')
    print(f"Generated graph: {graph_file}")
    print("Features: Space-efficient, straight edges, dense graphs")
    print()

def demonstrate_configuration_options():
    """
    Demonstrate how to access and modify configuration options.
    """
    
    print("Configuration Options")
    print("=" * 30)
    
    # Show available layouts
    from graphviz_config import list_available_layouts, DEFAULT_LAYOUT
    
    print(f"Current default layout: {DEFAULT_LAYOUT}")
    print()
    
    print("Available layout types:")
    layouts = list_available_layouts()
    for name, description in layouts.items():
        print(f"  {name}: {description}")
    print()
    
    # Show how to get specific configurations
    from graphviz_config import get_layout_config, get_node_config, get_edge_config
    
    print("Hierarchical layout configuration:")
    hierarchical_config = get_layout_config('hierarchical')
    for key, value in hierarchical_config.items():
        print(f"  {key}: {value}")
    print()
    
    print("Node styling configuration:")
    node_config = get_node_config()
    for key, value in node_config.items():
        print(f"  {key}: {value}")
    print()
    
    print("Edge styling configuration:")
    edge_config = get_edge_config()
    for key, value in edge_config.items():
        print(f"  {key}: {value}")

def before_and_after_comparison():
    """
    Show the differences between old and new configurations.
    """
    
    print("Before vs After Improvements")
    print("=" * 40)
    
    print("BEFORE (Original Configuration):")
    print("- Basic GraphViz Digraph with minimal configuration")
    print("- No edge routing optimization")
    print("- Default node spacing (often too close)")
    print("- Standard edge styling")
    print("- Chaotic edge crossings in complex graphs")
    print()
    
    print("AFTER (Improved Configuration):")
    print("- Hierarchical layout with orthogonal edge routing")
    print("- Optimized node spacing (ranksep=1.5, nodesep=1.0)")
    print("- Edge concentration to merge multiple connections")
    print("- Enhanced edge styling (penwidth=2, better arrows)")
    print("- Consistent node sizing for better alignment")
    print("- Multiple layout options for different use cases")
    print("- Configurable through external config file")
    print()

def quick_start_guide():
    """
    Quick start guide for using the improved GraphViz generator.
    """
    
    print("Quick Start Guide")
    print("=" * 20)
    
    print("1. Basic Usage (uses default hierarchical layout):")
    print("   controller = GraphVizGeneratorController('COMPONENT_NAME')")
    print("   graph_file = controller.generate_graph()")
    print()
    
    print("2. Specify Layout Type:")
    print("   graph_file = controller.generate_graph(layout_type='circular')")
    print()
    
    print("3. Change Default Layout (edit graphviz_config.py):")
    print("   DEFAULT_LAYOUT = 'hierarchical'  # Change to desired layout")
    print()
    
    print("4. Available Layout Types:")
    print("   - 'hierarchical': Best for dependency trees (default)")
    print("   - 'circular': Good for relationship visualization")
    print("   - 'force_directed': Good for complex networks")
    print("   - 'compact': Good for dense graphs")
    print()
    
    print("5. Key Improvements:")
    print("   - Cleaner edge routing with minimal crossings")
    print("   - Better node organization and spacing")
    print("   - Enhanced visual clarity while preserving original styling")

if __name__ == "__main__":
    print("GraphViz Dependency Diagram - Improved Configuration Examples")
    print("=" * 70)
    print()
    
    quick_start_guide()
    print()
    
    demonstrate_configuration_options()
    print()
    
    before_and_after_comparison()
    print()
    
    print("Note: To actually generate graphs, uncomment the following line:")
    print("# generate_dependency_diagram_examples()")
    print()
    print("The improvements focus on:")
    print("1. Better organized edges with cleaner paths")
    print("2. Minimized crossing lines where possible")
    print("3. Structured routing (orthogonal/hierarchical layouts)")
    print("4. Maintained readability while preserving original appearance")
