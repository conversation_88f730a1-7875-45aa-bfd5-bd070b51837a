import csv
import os
from django.http import HttpResponse
import datetime
from datetime import timedelta
import pytz
import json

import logging

import graphviz
from .graphviz_config import get_layout_config, get_node_config, get_edge_config

class GraphVizGeneratorController:
    CHAINS_DEPENDENCIES_FILE = "/tmp/poi181.j.basetwsd_mdmpa.json"

    def __init__(self, component):
        self.component = component.upper()

    @classmethod
    def generate_graph_for_component(cls, request=None, app=None, view=None, pk=None):
        generated_file = cls(pk).generate_graph()

        file = open(generated_file, "rb")
        return HttpResponse(file.read(), content_type="image/jpeg")

    def load_file_from_s3(self, output_filename=CHAINS_DEPENDENCIES_FILE):
        import boto3

        s3_client = boto3.client(
            "s3",
            aws_access_key_id=os.getenv("S3_CLIENT"),
            aws_secret_access_key=os.getenv("S3_SECRET"),
            use_ssl=False,
            endpoint_url=os.getenv("S3_URL"),
        )

        s3_client.download_file(
            Bucket=os.getenv("S3_BUCKET"),
            Key=f"poi181.j.basetwsd_mdmpa.json",
            Filename=output_filename,
        )

        return output_filename

    def load(self, file):
        try:
            with open(file, "r") as f:
                data = json.load(f)

            chains = {}

            for jst_obj in data:
                # Récupérer les attributs nécessaires de l'objet Jst
                chaine = jst_obj.get("chaine")
                owner = jst_obj.get("owner")
                workstation = jst_obj.get("workstation")
                description = jst_obj.get("description")
                follows = jst_obj.get("follows")

                # Vérifier si la chaîne existe déjà dans le dictionnaire chains
                if owner is not None and chaine not in chains:
                    chains[chaine] = {
                        "chain": chaine,
                        "component": owner,
                        "workstation": workstation,
                        "description": description,
                        "follows": follows,
                    }

            return chains
        except Exception as e:
            logging.error(f"Failed to load file : {e}")

    def flatten_chains(self, chains):
        uniq_chain = []

        for chain, data in chains.items():
            if chain not in uniq_chain:
                uniq_chain.append(chain)

        return uniq_chain

    def get_color_for_chain(self, chain):
        pass

    @classmethod
    def get_layout_config(cls, layout_type='hierarchical'):
        """
        Get GraphViz layout configuration for different visualization styles.

        :param layout_type: Type of layout ('hierarchical', 'circular', 'force_directed', 'compact')
        :return: Dictionary of GraphViz attributes
        """
        configs = {
            'hierarchical': {
                'layout': 'dot',
                'rankdir': 'TB',
                'ranksep': '1.5',
                'nodesep': '1.0',
                'splines': 'ortho',
                'concentrate': 'true',
                'overlap': 'false',
                'sep': '+20',
                'esep': '+10'
            },
            'circular': {
                'layout': 'circo',
                'splines': 'curved',
                'overlap': 'false',
                'sep': '+15',
                'concentrate': 'true'
            },
            'force_directed': {
                'layout': 'fdp',
                'splines': 'curved',
                'overlap': 'false',
                'sep': '+25',
                'K': '2.0'  # Spring constant for force-directed layout
            },
            'compact': {
                'layout': 'neato',
                'splines': 'line',
                'overlap': 'false',
                'sep': '+10',
                'concentrate': 'true'
            }
        }
        return configs.get(layout_type, configs['hierarchical'])

    @classmethod
    def get_fillcolor_for_chain(cls, chain):
        """
        Return color for a specific chain
        :param chain:
        :return:
        """
        colors = {
            "D": "lightyellow1",
            "J": "lightskyblue3",
            "V": "lightskyblue4",
            "P": "lightskyblue4",
            "G": "lightskyblue4",
            "E": "lightyellow1",
            "K": "green3",
            "W": "green4",
            "Q": "green4",
            "H": "green4",
            "M": "grey",
        }

        return colors.get(chain[-1], "white")

    def file_exists_since_mins(self, filename):
        try:
            return (
                datetime.datetime.now()
                - datetime.datetime.fromtimestamp(os.path.getctime(filename))
            ) / timedelta(minutes=1)
        except:
            return None

    def generate_graph(self, output_dir="/tmp", file_format="png", layout_type="hierarchical"):
        """
        Generate a dependency graph with enhanced layout and edge organization.

        :param output_dir: Directory to save the generated graph
        :param file_format: Output format (png, svg, pdf, etc.)
        :param layout_type: Layout algorithm to use:
            - 'hierarchical': Top-down layout with orthogonal edges (best for dependency trees)
            - 'circular': Circular layout with curved edges (good for showing relationships)
            - 'force_directed': Physics-based layout (good for complex networks)
            - 'compact': Compact layout with straight edges (good for dense graphs)
        :return: Path to the generated graph file

        Layout improvements include:
        - Orthogonal edge routing for cleaner lines
        - Better node spacing to reduce overlaps
        - Edge concentration to merge multiple connections
        - Consistent node sizing for better alignment
        - Enhanced edge styling for better visibility
        """
        try: 
            final_filename = f"{output_dir}/chains-{self.component.lower()}.gv.{file_format}"

            # We check if the graph for the component already exists
            if (self.file_exists_since_mins(final_filename) or ********) < (60 * 4):
                return final_filename

            # Check if chains file is existing and recent.
            if (self.file_exists_since_mins(self.CHAINS_DEPENDENCIES_FILE) or ********) < (60 * 4):
                file_path = self.CHAINS_DEPENDENCIES_FILE
            else:
                print("Download file ...")
                file_path = self.load_file_from_s3()

            try: 
                chains = self.load(file_path)

                filtered_chains = dict(filter(lambda pair: pair[1]["component"].lower() == self.component.lower(), chains.items()))
            except Exception as load_error:
                # Log the error and return None or raise a specific exception
                logging.error(f"An error occurred while loading and filtering chains: {load_error}")
                return None
            
            # Diagram generation
            if (self.file_exists_since_mins(final_filename) or ********) < (1 * 4):
                file_creation_date = datetime.datetime.now() - timedelta(minutes=self.file_exists_since_mins(final_filename))
            else:
                file_creation_date = datetime.datetime.now()

            local_timezone = pytz.timezone('UTC')  # ou pytz.timezone('Etc/UTC') pour l'heure UTC

            file_creation_date_local = local_timezone.localize(file_creation_date)
            france_timezone = pytz.timezone('Europe/Paris')
            file_creation_date_fr = file_creation_date_local.astimezone(france_timezone)
            file_creation_date_label = file_creation_date_fr.strftime("%d/%m/%Y à %H:%M:%S")

            try:
                output_diagram  = graphviz.Digraph(f'chains-{self.component.lower()}', format=file_format)

                # Get enhanced layout configuration for better edge organization
                layout_config = get_layout_config(layout_type)

                # Apply layout configuration
                output_diagram.attr(**layout_config)

                # Apply label configuration (preserved from original)
                output_diagram.attr(
                    label=f"<<FONT POINT-SIZE='40'>Date d'actualisation: {file_creation_date_label}</FONT>>",
                    labelloc="tl",
                    labeljust="left"
                )

                # Get node configuration
                node_config = get_node_config()

                for key, chain in sorted(filtered_chains.items()):
                    output_diagram.node(
                        key,
                        shape="oval",
                        color=self.get_color_for_chain(key),
                        style="filled",
                        fillcolor=self.get_fillcolor_for_chain(key),
                        label=f"{key}\n{chain['description']}",
                        **node_config  # Apply enhanced node attributes
                    )

                added_edges = set()
                # Get edge configuration
                edge_config = get_edge_config()

                for key, chain in sorted(filtered_chains.items()):
                    for dep in chain['follows']:
                        for clé, value in filtered_chains.items():
                            if dep in value['follows'] and value["chain"] != key:
                                edge_key = (min(value["chain"], key), max(value["chain"], key))
                                if edge_key not in added_edges:
                                    added_edges.add(edge_key)
                                    # Enhanced edge styling for better visibility and organization
                                    output_diagram.edge(
                                        value["chain"],
                                        key,
                                        color='black',
                                        **edge_config  # Apply enhanced edge attributes
                                    )


                output_diagram.render(directory=output_dir).replace('\\', '/')

            except Exception as diagram_error:
                logging.error(f"An error occurred while generating the diagram: {diagram_error}")
                return None
            
            return final_filename
        
        except Exception as e:
            logging.error(f"An unexpected error occurred: {e}")
            return None