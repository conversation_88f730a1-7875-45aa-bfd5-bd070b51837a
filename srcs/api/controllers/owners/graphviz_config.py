"""
GraphViz Configuration for Dependency Diagrams

This module provides configuration options for improving the visual organization
of dependency diagrams generated by GraphViz. The configurations focus on:
- Better edge routing and organization
- Minimizing line crossings
- Structured layouts for improved readability
- Maintaining original graph appearance while improving clarity

Usage:
    To change the default layout, modify the DEFAULT_LAYOUT constant below.
    Available layouts: 'hierarchical', 'circular', 'force_directed', 'compact'
"""

# Default layout type for dependency diagrams
# Change this to switch between different layout algorithms
DEFAULT_LAYOUT = 'hierarchical'

# Layout-specific configurations
LAYOUT_CONFIGS = {
    'hierarchical': {
        'description': 'Top-down hierarchical layout with orthogonal edges - best for dependency trees',
        'layout': 'dot',
        'rankdir': 'TB',  # Top-to-bottom (change to 'LR' for left-to-right)
        'ranksep': '1.5',  # Separation between ranks
        'nodesep': '1.0',  # Separation between nodes on same rank
        'splines': 'ortho',  # Orthogonal edge routing for clean lines
        'concentrate': 'true',  # Merge multiple edges between same nodes
        'overlap': 'false',  # Prevent node overlaps
        'sep': '+20',  # Additional separation between components
        'esep': '+10',  # Additional separation for edges
    },
    
    'circular': {
        'description': 'Circular layout with curved edges - good for showing relationships',
        'layout': 'circo',
        'splines': 'curved',  # Curved edges for circular layout
        'overlap': 'false',
        'sep': '+15',
        'concentrate': 'true',
    },
    
    'force_directed': {
        'description': 'Physics-based layout - good for complex networks',
        'layout': 'fdp',
        'splines': 'curved',  # Curved edges work well with force-directed
        'overlap': 'false',
        'sep': '+25',  # More separation needed for force-directed
        'K': '2.0',  # Spring constant for force-directed layout
    },
    
    'compact': {
        'description': 'Compact layout with straight edges - good for dense graphs',
        'layout': 'neato',
        'splines': 'line',  # Straight lines for compact layout
        'overlap': 'false',
        'sep': '+10',
        'concentrate': 'true',
    }
}

# Node styling configuration
NODE_CONFIG = {
    'margin': '0.2,0.1',  # Internal margin for better text spacing
    'fontsize': '12',  # Consistent font size
    'width': '1.5',  # Minimum width for consistent node sizing
    'height': '1.0',  # Minimum height for consistent node sizing
}

# Edge styling configuration
EDGE_CONFIG = {
    'penwidth': '2',  # Line thickness for better visibility
    'arrowsize': '0.8',  # Appropriate arrow size
    'arrowhead': 'normal',  # Clear arrow direction
}

# Alternative layout options for specific use cases
ALTERNATIVE_CONFIGS = {
    'left_to_right': {
        'description': 'Left-to-right hierarchical layout',
        'layout': 'dot',
        'rankdir': 'LR',  # Left-to-right instead of top-to-bottom
        'ranksep': '2.0',
        'nodesep': '1.2',
        'splines': 'ortho',
        'concentrate': 'true',
        'overlap': 'false',
        'sep': '+25',
        'esep': '+15',
    },
    
    'minimal_crossings': {
        'description': 'Optimized for minimal edge crossings',
        'layout': 'dot',
        'rankdir': 'TB',
        'ranksep': '2.0',  # Increased separation to reduce crossings
        'nodesep': '1.5',
        'splines': 'spline',  # Smooth splines instead of orthogonal
        'concentrate': 'true',
        'overlap': 'false',
        'ordering': 'out',  # Order nodes to minimize crossings
        'sep': '+30',
        'esep': '+20',
    }
}

def get_layout_config(layout_type=None):
    """
    Get configuration for specified layout type.
    
    :param layout_type: Layout type to use, defaults to DEFAULT_LAYOUT
    :return: Dictionary of GraphViz attributes
    """
    if layout_type is None:
        layout_type = DEFAULT_LAYOUT
    
    # Check main configs first, then alternatives
    if layout_type in LAYOUT_CONFIGS:
        return LAYOUT_CONFIGS[layout_type]
    elif layout_type in ALTERNATIVE_CONFIGS:
        return ALTERNATIVE_CONFIGS[layout_type]
    else:
        # Fallback to hierarchical if unknown layout requested
        return LAYOUT_CONFIGS['hierarchical']

def get_node_config():
    """Get node styling configuration."""
    return NODE_CONFIG.copy()

def get_edge_config():
    """Get edge styling configuration."""
    return EDGE_CONFIG.copy()

def list_available_layouts():
    """List all available layout types with descriptions."""
    layouts = {}
    for name, config in LAYOUT_CONFIGS.items():
        layouts[name] = config.get('description', 'No description available')
    for name, config in ALTERNATIVE_CONFIGS.items():
        layouts[name] = config.get('description', 'No description available')
    return layouts
